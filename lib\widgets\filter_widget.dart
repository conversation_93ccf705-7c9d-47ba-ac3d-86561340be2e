import 'package:flutter/material.dart';
import 'dart:async'; // Import for Timer
import '../controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;

class FilterWidget extends StatefulWidget {
  final filter_ctrl.FilterController controller;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;

  const FilterWidget({
    super.key,
    required this.controller,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
  });

  @override
  State<FilterWidget> createState() => _FilterWidgetState();
}

class _FilterWidgetState extends State<FilterWidget> {
  late TextEditingController _searchController;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(
      text: widget.controller.filter.name,
    );
    widget.controller.addListener(_onFilterChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    widget.controller.removeListener(_onFilterChanged);
    super.dispose();
  }

  void _onFilterChanged() {
    // Update the text field if the filter name changes externally (e.g., reset button)
    if (_searchController.text != widget.controller.filter.name) {
      _searchController.text = widget.controller.filter.name ?? '';
    }
  }

  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      widget.controller.setNameFilter(value.isEmpty ? null : value);
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, _) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: 'Search by name',
                      hintText: 'Enter channel name',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon:
                          widget.controller.filter.name != null &&
                                  widget.controller.filter.name!.isNotEmpty
                              ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                widget.controller.setNameFilter(null);
                              },
                                )
                              : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.blueGrey.shade50,
                  ), // Closing parenthesis for InputDecoration
                  onChanged: _onSearchChanged,
                  onSubmitted: (value) {
                    // Trigger search immediately on submit
                    _debounce?.cancel();
                    widget.controller.setNameFilter(
                      value.isEmpty ? null : value,
                    );
                  },
                ), // Closing parenthesis for TextField
                const SizedBox(
                  height: 16,
                ), // Space between search and filters
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      DropdownButton<int?>(
                        value: widget.controller.filter.categoryId,
                        hint: const Text('Category'),
                        items: [
                          const DropdownMenuItem(
                            value: null,
                            child: Text('All Categories'),
                          ),
                          ...widget.categories.map(
                            (cat) => DropdownMenuItem(
                              value: cat.id,
                              child: Text(cat.name),
                            ),
                          ),
                        ],
                        onChanged: (val) {
                          if (val == null) {
                            widget.controller.clearFilter(category: true);
                          } else {
                            widget.controller.setFilter(categoryId: val);
                          }
                        },
                      ),
                      const SizedBox(width: 16),
                      DropdownButton<String?>(
                        value: widget.controller.filter.region,
                        hint: const Text('Region'),
                        items: [
                          const DropdownMenuItem(
                            value: null,
                            child: Text('All Regions'),
                          ),
                          ...widget.regions.map(
                            (r) => DropdownMenuItem(
                              value: r.code,
                              child: Text(r.name),
                            ),
                          ),
                        ],
                        onChanged: (val) {
                          if (val == null) {
                            widget.controller.clearFilter(region: true);
                          } else {
                            widget.controller.setFilter(
                              region: val,
                              country: null,
                            ); // Reset country on region change
                          }
                        },
                      ),
                      const SizedBox(width: 16),
                      DropdownButton<String?>(
                        value: widget.controller.filter.country,
                        hint: const Text('Country'),
                        items: [
                          const DropdownMenuItem(
                            value: null,
                            child: Text('All Countries'),
                          ),
                          ...widget.countries.map(
                            (c) => DropdownMenuItem(
                              value: c.code,
                              child: Row(
                                children: [
                                  if (c.flag.isNotEmpty) Text(c.flag),
                                  if (c.flag.isNotEmpty)
                                    const SizedBox(width: 4),
                                  Text(c.name),
                                ],
                              ),
                            ),
                          ),
                        ],
                        onChanged: (val) {
                          if (val == null) {
                            widget.controller.clearFilter(country: true);
                          } else {
                            widget.controller.setFilter(country: val);
                          }
                        },
                      ),
                      const SizedBox(width: 16),
                      DropdownButton<String?>(
                        value: widget.controller.filter.language,
                        hint: const Text('Language'),
                        items: [
                          const DropdownMenuItem(
                            value: null,
                            child: Text('All Languages'),
                          ),
                          ...widget.languages.map(
                            (l) => DropdownMenuItem(
                              value: l.code,
                              child: Text(l.name),
                            ),
                          ),
                        ],
                        onChanged: (val) {
                          if (val == null) {
                            widget.controller.clearFilter(language: true);
                          } else {
                            widget.controller.setFilter(language: val);
                          }
                        },
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          widget.controller.resetAll();
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Reset'),
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          backgroundColor: Colors.blueGrey.shade50,
                          foregroundColor: Colors.blueGrey.shade900,
                          elevation: 0,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
