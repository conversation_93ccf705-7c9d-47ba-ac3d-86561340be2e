import 'package:sqflite/sqflite.dart';
import '../models/channel.dart';
import '../models/channel_filter.dart';

class ChannelRepository {
  final Database db;

  ChannelRepository(this.db);

  Future<List<Channel>> getAllChannels() async {
    final result = await db.query(
      'channels',
      where: 'is_active = ?',
      whereArgs: [1],
    );
    return result.map((row) => Channel.fromMap(row)).toList();
  }

  Future<List<Channel>> getChannelsByCategory(int categoryId) async {
    final result = await db.query(
      'channels',
      where: 'category_id = ? AND is_active = ?',
      whereArgs: [categoryId, 1],
    );
    return result.map((row) => Channel.fromMap(row)).toList();
  }

  Future<List<Channel>> getChannelsByCountry(String countryCode) async {
    final result = await db.query(
      'channels',
      where: 'country_code = ? AND is_active = ?',
      whereArgs: [countryCode, 1],
    );
    return result.map((row) => Channel.fromMap(row)).toList();
  }

  Future<List<Channel>> getChannelsByLanguage(String languageCode) async {
    // first get the country codes for the given language code from the table country_languages
    final countryCodesResult = await db.query(
      'country_languages',
      columns: ['country_code'],
      where: 'language_code = ?',
      whereArgs: [languageCode],
    );

    // extract the country codes from the result
    final countryCodes =
        countryCodesResult.map((row) => row['country_code'] as String).toList();

    // now get the channels for those country codes
    final result = await db.query(
      'channels',
      where:
          'country_code IN (${List.filled(countryCodes.length, '?').join(',')}) AND is_active = ?',
      whereArgs: [...countryCodes, 1],
    );
    return result.map((row) => Channel.fromMap(row)).toList();
  }

  Future<List<String>> getChannelSources(String channelId) async {
    final result = await db.rawQuery(
      '''
      SELECT source_url
      FROM channel_sources
      WHERE channel_id = ? AND is_active = 1
      ''',
      [channelId],
    );
    return result.map((row) => row['source_url'] as String).toList();
  }

  Future<List<Channel>> getChannelsPaged({
    int limit = 20,
    int offset = 0,
    ChannelFilter? filter,
  }) async {
    // Build where clause and args based on filter
    final whereClauses = <String>[];
    final whereArgs = <dynamic>[];
    whereClauses.add('is_active = ?');
    whereArgs.add(1);
    if (filter != null) {
      if (filter.region != null && filter.region!.isNotEmpty) {
        // Get country codes for the selected region
        final regionCountriesResult = await db.query(
          'region_countries',
          columns: ['country_code'],
          where: 'region_code = ?',
          whereArgs: [filter.region],
        );
        final regionCountryCodes =
            regionCountriesResult
                .map((row) => row['country_code'] as String)
                .toList();
        if (regionCountryCodes.isNotEmpty) {
          whereClauses.add(
            'country_code IN (${List.filled(regionCountryCodes.length, '?').join(',')})',
          );
          whereArgs.addAll(regionCountryCodes);
        } else {
          // No countries for this region, return empty
          return [];
        }
      }
      if (filter.country != null && filter.country!.isNotEmpty) {
        whereClauses.add('country_code = ?');
        whereArgs.add(filter.country);
      }
      if (filter.language != null && filter.language!.isNotEmpty) {
        // Get country codes for the language
        final countryCodesResult = await db.query(
          'country_languages',
          columns: ['country_code'],
          where: 'language_code = ?',
          whereArgs: [filter.language],
        );
        final countryCodes =
            countryCodesResult
                .map((row) => row['country_code'] as String)
                .toList();
        if (countryCodes.isNotEmpty) {
          whereClauses.add(
            'country_code IN (${List.filled(countryCodes.length, '?').join(',')})',
          );
          whereArgs.addAll(countryCodes);
        } else {
          // No countries for this language, return empty
          return [];
        }
      }
      if (filter.categoryId != null && filter.categoryId != -1) {
        whereClauses.add('category_id = ?');
        whereArgs.add(filter.categoryId);
      }
      if (filter.name != null && filter.name!.isNotEmpty) {
        whereClauses.add('(name LIKE ? OR alt_names LIKE ?)');
        whereArgs.addAll(['%${filter.name}%', '%${filter.name}%']);
      }
    }
    final result = await db.query(
      'channels',
      where: whereClauses.join(' AND '),
      whereArgs: whereArgs,
      limit: limit,
      offset: offset,
    );
    return result.map((row) => Channel.fromMap(row)).toList();
  }
}
