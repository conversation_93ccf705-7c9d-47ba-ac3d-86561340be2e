import 'package:flutter/foundation.dart';
import '../models/channel_filter.dart';

class FilterController extends ChangeNotifier {
  ChannelFilter _filter = const ChannelFilter();

  ChannelFilter get filter => _filter;

  void setFilter({
    String? region,
    String? country,
    String? language,
    String? name,
    int? categoryId,
  }) {
    _filter = ChannelFilter(
      region: region ?? _filter.region,
      country: country ?? _filter.country,
      language: language ?? _filter.language,
      name: name ?? _filter.name,
      categoryId: categoryId ?? _filter.categoryId,
    );
    notifyListeners();
  }

  void setNameFilter(String? name) {
    _filter = _filter.copyWith(name: name);
    notifyListeners();
  }

  void clearFilter({
    bool region = false,
    bool country = false,
    bool language = false,
    bool name = false,
    bool category = false,
  }) {
    _filter = ChannelFilter(
      region: region ? null : _filter.region,
      country: country ? null : _filter.country,
      language: language ? null : _filter.language,
      name: name ? null : _filter.name,
      categoryId: category ? null : _filter.categoryId,
    );
    notifyListeners();
  }

  void resetAll() {
    _filter = const ChannelFilter();
    notifyListeners();
  }
}
